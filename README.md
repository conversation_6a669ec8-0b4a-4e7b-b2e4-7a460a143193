# 园区能耗统计分析H5应用

基于Vue3 + Vant UI + Node.js开发的园区能耗统计分析移动端应用，包含完整的前后端实现。

## 🚀 项目特色

- **现代化技术栈**: Vue3 + JavaScript + Vant UI + Node.js
- **移动端优化**: 响应式设计，完美适配手机和平板
- **完整功能模块**: 8大核心功能模块，覆盖能耗管理全流程
- **Mock数据**: 无需数据库，使用Mock数据快速演示
- **即开即用**: 简单配置即可运行

## 📱 功能模块

### 1. 仪表盘概览
- 实时能耗数据展示
- 关键指标卡片
- 系统状态监控
- 预警信息提醒
- 快速操作入口

### 2. 能耗统计
- 按系统/区域/时间统计
- 饼图数据可视化
- 实时数据监控
- 筛选和导出功能

### 3. 趋势分析
- 多时间维度趋势图
- 峰值和时段分析
- 系统对比分析
- 趋势要点总结

### 4. 对比分析
- 同比环比对比
- 自定义时段对比
- 变化趋势指示
- 对比结果总结

### 5. 报表管理
- 快速导出报表
- 报表模板管理
- 导出记录查看
- 多格式支持

### 6. 智能预测
- AI能耗预测
- 预警阈值设置
- 预测准确率展示
- 历史预测记录

### 7. 视频监控
- 实时视频监控
- 预警视频回放
- 设备状态管理
- 全屏播放支持

### 8. 系统设置
- 监控参数配置
- 通知方式设置
- 用户权限管理
- 系统信息查看

## 🛠️ 技术架构

### 前端技术栈
- **Vue 3**: 渐进式JavaScript框架
- **Vant UI**: 移动端Vue组件库
- **Vue Router**: 单页面应用路由
- **Axios**: HTTP客户端
- **Vite**: 现代化构建工具

### 后端技术栈
- **Node.js**: JavaScript运行环境
- **Express**: Web应用框架
- **CORS**: 跨域资源共享
- **Body-parser**: 请求体解析

## 📦 项目结构

```
├── energy-consumption-h5/          # 前端项目
│   ├── src/
│   │   ├── views/                  # 页面组件
│   │   │   ├── Dashboard.vue       # 仪表盘
│   │   │   ├── Statistics.vue      # 能耗统计
│   │   │   ├── Trends.vue          # 趋势分析
│   │   │   ├── Compare.vue         # 对比分析
│   │   │   ├── Reports.vue         # 报表管理
│   │   │   ├── Prediction.vue      # 智能预测
│   │   │   ├── Video.vue           # 视频监控
│   │   │   └── Settings.vue        # 系统设置
│   │   ├── utils/
│   │   │   └── mockApi.js          # Mock API
│   │   ├── App.vue                 # 主应用组件
│   │   └── main.js                 # 应用入口
│   ├── package.json
│   └── vite.config.js
├── backend/                        # 后端项目
│   ├── server.js                   # 服务器主文件
│   └── package.json
├── energy-consumption-app-prototype.html  # 原型设计
└── README.md
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend

# 安装依赖
npm install

# 启动服务器
npm start
```

后端服务将在 `http://localhost:3001` 启动

### 2. 启动前端应用

```bash
# 进入前端目录
cd energy-consumption-h5

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 `http://localhost:5173` 启动

### 3. 访问应用

打开浏览器访问 `http://localhost:5173`，即可体验完整的H5应用。

## 📱 移动端体验

1. 在浏览器中打开开发者工具
2. 切换到移动设备模拟模式
3. 选择手机设备尺寸（推荐iPhone 12 Pro或类似尺寸）
4. 刷新页面即可体验移动端效果

## 🔧 API接口

后端提供以下API接口：

- `GET /api/dashboard` - 获取仪表盘数据
- `GET /api/statistics` - 获取统计数据
- `GET /api/trends` - 获取趋势数据
- `GET /api/compare` - 获取对比数据
- `GET /api/reports` - 获取报表数据
- `GET /api/prediction` - 获取预测数据
- `GET /api/video` - 获取视频监控数据
- `GET /api/settings` - 获取设置数据
- `GET /health` - 健康检查

## 🎨 UI设计特色

- **移动端优先**: 专为移动设备优化的界面设计
- **Vant UI组件**: 使用成熟的移动端组件库
- **响应式布局**: 自适应不同屏幕尺寸
- **现代化风格**: 简洁美观的视觉设计
- **交互友好**: 符合移动端操作习惯

## 📊 数据可视化

- 使用图表占位符，便于集成真实图表库
- 支持饼图、柱状图、折线图等多种图表类型
- 实时数据更新和动态展示
- 移动端优化的图表交互

## 🔒 开发建议

### 生产环境部署
1. 使用真实的数据库替换Mock数据
2. 添加用户认证和权限控制
3. 集成真实的图表库（如ECharts）
4. 添加错误处理和日志记录
5. 配置HTTPS和安全策略

### 功能扩展
1. 添加离线缓存支持
2. 集成推送通知
3. 添加数据导出功能
4. 集成地图显示
5. 添加多语言支持

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**注意**: 这是一个演示项目，使用Mock数据。在生产环境中请替换为真实的数据源和API接口。
