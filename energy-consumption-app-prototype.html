<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>园区能耗统计分析App - 原型设计</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .screen {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e1e8ed;
        }

        .screen-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .screen-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .screen-content {
            padding: 20px;
            height: 500px;
            overflow-y: auto;
        }

        .nav-bar {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 5px;
            margin-bottom: 20px;
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .nav-item.active {
            background: #667eea;
            color: white;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 12px;
            margin-bottom: 15px;
        }

        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .chart-placeholder {
            height: 200px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
            margin: 15px 0;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-normal {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
        }

        .video-placeholder {
            height: 200px;
            background: #000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            margin-bottom: 15px;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .grid-3 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .text-center {
            text-align: center;
        }

        .mb-3 {
            margin-bottom: 15px;
        }

        .header-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 300;
        }

        .header-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 16px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="header-title">园区能耗统计分析App</h1>
        <p class="header-subtitle">完整原型界面设计 - 可直接用于开发</p>

        <div class="prototype-grid">

            <!-- 1. 仪表盘/概览页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-tachometer-alt"></i>
                        仪表盘概览
                    </div>
                    <div>
                        <i class="fas fa-bell"></i>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
                <div class="screen-content">
                    <div class="grid-3 mb-3">
                        <div class="metric-card">
                            <div class="metric-value">1,245.6</div>
                            <div class="metric-label">今日总能耗 (kWh)</div>
                        </div>
                        <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <div class="metric-value">856.2</div>
                            <div class="metric-label">本月累计 (kWh)</div>
                        </div>
                        <div class="metric-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                            <div class="metric-value">-12.5%</div>
                            <div class="metric-label">同比变化</div>
                        </div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-chart-line"></i> 实时能耗趋势</h4>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area fa-2x"></i>
                            <span style="margin-left: 10px;">24小时能耗趋势图</span>
                        </div>
                    </div>

                    <div class="grid-2">
                        <div class="card">
                            <h5><i class="fas fa-thermometer-half"></i> 控温系统</h5>
                            <div class="metric-value" style="color: #667eea; font-size: 20px;">456.8 kWh</div>
                            <div class="status-badge status-normal">正常运行</div>
                        </div>
                        <div class="card">
                            <h5><i class="fas fa-wind"></i> 通风系统</h5>
                            <div class="metric-value" style="color: #667eea; font-size: 20px;">234.5 kWh</div>
                            <div class="status-badge status-warning">轻微异常</div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>电梯系统能耗超出预警线15%，建议检查</span>
                    </div>
                </div>
            </div>

            <!-- 2. 能耗统计页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-chart-bar"></i>
                        能耗统计
                    </div>
                    <button class="btn btn-primary" style="font-size: 12px;">
                        <i class="fas fa-filter"></i> 筛选
                    </button>
                </div>
                <div class="screen-content">
                    <div class="nav-bar">
                        <div class="nav-item active">按系统</div>
                        <div class="nav-item">按区域</div>
                        <div class="nav-item">按时间</div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-pie-chart"></i> 系统能耗分布</h4>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-pie fa-2x"></i>
                            <span style="margin-left: 10px;">饼图：各系统能耗占比</span>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>系统名称</th>
                                <th>今日能耗</th>
                                <th>占比</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><i class="fas fa-thermometer-half"></i> 控温系统</td>
                                <td>456.8 kWh</td>
                                <td>36.7%</td>
                                <td><span class="status-badge status-normal">正常</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-wind"></i> 通风系统</td>
                                <td>234.5 kWh</td>
                                <td>18.8%</td>
                                <td><span class="status-badge status-warning">异常</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-elevator"></i> 电梯系统</td>
                                <td>189.3 kWh</td>
                                <td>15.2%</td>
                                <td><span class="status-badge status-danger">超标</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-lightbulb"></i> 照明系统</td>
                                <td>156.7 kWh</td>
                                <td>12.6%</td>
                                <td><span class="status-badge status-normal">正常</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-plug"></i> 其他设备</td>
                                <td>208.3 kWh</td>
                                <td>16.7%</td>
                                <td><span class="status-badge status-normal">正常</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 3. 趋势分析页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-chart-line"></i>
                        趋势分析
                    </div>
                    <div>
                        <button class="btn btn-primary" style="font-size: 12px;">
                            <i class="fas fa-calendar"></i> 时间范围
                        </button>
                    </div>
                </div>
                <div class="screen-content">
                    <div class="nav-bar">
                        <div class="nav-item active">日趋势</div>
                        <div class="nav-item">周趋势</div>
                        <div class="nav-item">月趋势</div>
                        <div class="nav-item">年趋势</div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-chart-area"></i> 能耗趋势图</h4>
                        <div class="chart-placeholder" style="height: 250px;">
                            <i class="fas fa-chart-line fa-3x"></i>
                            <span style="margin-left: 15px;">多系统能耗趋势对比图</span>
                        </div>
                    </div>

                    <div class="grid-2">
                        <div class="card">
                            <h5><i class="fas fa-arrow-trend-up"></i> 峰值分析</h5>
                            <p><strong>今日峰值：</strong> 14:30 - 1,456 kWh</p>
                            <p><strong>平均值：</strong> 1,245 kWh</p>
                            <p><strong>最低值：</strong> 03:15 - 856 kWh</p>
                        </div>
                        <div class="card">
                            <h5><i class="fas fa-clock"></i> 时段分析</h5>
                            <p><strong>工作时段：</strong> 08:00-18:00</p>
                            <p><strong>平均能耗：</strong> 1,350 kWh/h</p>
                            <p><strong>非工作时段：</strong> 18:00-08:00</p>
                            <p><strong>平均能耗：</strong> 890 kWh/h</p>
                        </div>
                    </div>

                    <div class="card">
                        <h5><i class="fas fa-list"></i> 趋势要点</h5>
                        <ul style="margin-left: 20px; line-height: 1.6;">
                            <li>控温系统在14:00-16:00期间能耗最高</li>
                            <li>通风系统存在异常波动，建议检查</li>
                            <li>电梯系统在上下班高峰期能耗激增</li>
                            <li>照明系统能耗相对稳定</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 4. 对比分析页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-balance-scale"></i>
                        对比分析
                    </div>
                    <button class="btn btn-primary" style="font-size: 12px;">
                        <i class="fas fa-cog"></i> 设置对比
                    </button>
                </div>
                <div class="screen-content">
                    <div class="nav-bar">
                        <div class="nav-item active">同比分析</div>
                        <div class="nav-item">环比分析</div>
                        <div class="nav-item">自定义对比</div>
                    </div>

                    <div class="grid-2 mb-3">
                        <div class="form-group">
                            <label class="form-label">对比时段A</label>
                            <input type="date" class="form-control" value="2024-06-01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">对比时段B</label>
                            <input type="date" class="form-control" value="2023-06-01">
                        </div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-chart-column"></i> 同比对比图表</h4>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-column fa-2x"></i>
                            <span style="margin-left: 10px;">柱状图：同期能耗对比</span>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>系统</th>
                                <th>今年同期</th>
                                <th>去年同期</th>
                                <th>变化率</th>
                                <th>趋势</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>控温系统</td>
                                <td>456.8 kWh</td>
                                <td>523.2 kWh</td>
                                <td style="color: #28a745;">-12.7%</td>
                                <td><i class="fas fa-arrow-down" style="color: #28a745;"></i></td>
                            </tr>
                            <tr>
                                <td>通风系统</td>
                                <td>234.5 kWh</td>
                                <td>198.3 kWh</td>
                                <td style="color: #dc3545;">+18.3%</td>
                                <td><i class="fas fa-arrow-up" style="color: #dc3545;"></i></td>
                            </tr>
                            <tr>
                                <td>电梯系统</td>
                                <td>189.3 kWh</td>
                                <td>176.8 kWh</td>
                                <td style="color: #dc3545;">+7.1%</td>
                                <td><i class="fas fa-arrow-up" style="color: #dc3545;"></i></td>
                            </tr>
                            <tr>
                                <td>照明系统</td>
                                <td>156.7 kWh</td>
                                <td>162.4 kWh</td>
                                <td style="color: #28a745;">-3.5%</td>
                                <td><i class="fas fa-arrow-down" style="color: #28a745;"></i></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 5. 报表管理页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-file-alt"></i>
                        报表管理
                    </div>
                    <button class="btn btn-success" style="font-size: 12px;">
                        <i class="fas fa-plus"></i> 新建报表
                    </button>
                </div>
                <div class="screen-content">
                    <div class="nav-bar">
                        <div class="nav-item active">报表列表</div>
                        <div class="nav-item">模板管理</div>
                        <div class="nav-item">导出记录</div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-download"></i> 快速导出</h4>
                        <div class="grid-2">
                            <div class="form-group">
                                <label class="form-label">报表类型</label>
                                <select class="form-control">
                                    <option>日报表</option>
                                    <option>周报表</option>
                                    <option>月报表</option>
                                    <option>年报表</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">导出格式</label>
                                <select class="form-control">
                                    <option>Excel (.xlsx)</option>
                                    <option>PDF (.pdf)</option>
                                    <option>CSV (.csv)</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn btn-primary">
                            <i class="fas fa-download"></i> 立即导出
                        </button>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>报表名称</th>
                                <th>类型</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>6月能耗统计报表</td>
                                <td>月报表</td>
                                <td>2024-07-01 09:30</td>
                                <td><span class="status-badge status-normal">已完成</span></td>
                                <td>
                                    <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>系统对比分析报表</td>
                                <td>自定义</td>
                                <td>2024-06-30 16:45</td>
                                <td><span class="status-badge status-normal">已完成</span></td>
                                <td>
                                    <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>异常能耗分析报表</td>
                                <td>专项报表</td>
                                <td>2024-06-29 14:20</td>
                                <td><span class="status-badge status-warning">生成中</span></td>
                                <td>
                                    <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;"
                                        disabled>
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 6. 智能预测页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-brain"></i>
                        智能预测
                    </div>
                    <button class="btn btn-warning" style="font-size: 12px;">
                        <i class="fas fa-sync"></i> 更新模型
                    </button>
                </div>
                <div class="screen-content">
                    <div class="nav-bar">
                        <div class="nav-item active">能耗预测</div>
                        <div class="nav-item">预警设置</div>
                        <div class="nav-item">模型管理</div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-crystal-ball"></i> 未来7天能耗预测</h4>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line fa-2x"></i>
                            <span style="margin-left: 10px;">预测曲线图：基于历史数据的AI预测</span>
                        </div>
                    </div>

                    <div class="grid-2">
                        <div class="card">
                            <h5><i class="fas fa-exclamation-triangle"></i> 预警设置</h5>
                            <div class="form-group">
                                <label class="form-label">预警阈值 (%)</label>
                                <input type="number" class="form-control" value="15" min="0" max="100">
                            </div>
                            <div class="form-group">
                                <label class="form-label">预警方式</label>
                                <div>
                                    <label><input type="checkbox" checked> 邮件通知</label><br>
                                    <label><input type="checkbox" checked> 短信通知</label><br>
                                    <label><input type="checkbox" checked> 视频弹窗</label>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <h5><i class="fas fa-chart-bar"></i> 预测准确率</h5>
                            <div class="metric-value" style="color: #28a745; font-size: 24px;">92.5%</div>
                            <p style="font-size: 14px; color: #666;">基于过去30天数据</p>
                            <div class="progress" style="height: 8px; background: #e9ecef; border-radius: 4px;">
                                <div style="width: 92.5%; height: 100%; background: #28a745; border-radius: 4px;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>预测显示明日14:00-16:00电梯系统可能超出预警线25%</span>
                    </div>
                </div>
            </div>

            <!-- 7. 视频监控页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-video"></i>
                        视频监控
                    </div>
                    <div>
                        <button class="btn btn-danger" style="font-size: 12px;">
                            <i class="fas fa-exclamation-triangle"></i> 3个预警
                        </button>
                    </div>
                </div>
                <div class="screen-content">
                    <div class="nav-bar">
                        <div class="nav-item active">实时监控</div>
                        <div class="nav-item">预警回放</div>
                        <div class="nav-item">设备管理</div>
                    </div>

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>电梯机房能耗异常，已触发视频监控</span>
                        <button class="btn btn-danger" style="margin-left: 10px; padding: 5px 10px; font-size: 12px;">
                            查看详情
                        </button>
                    </div>

                    <div class="grid-2">
                        <div class="card">
                            <h5><i class="fas fa-elevator"></i> 电梯机房 - 预警中</h5>
                            <div class="video-placeholder">
                                <i class="fas fa-play-circle fa-3x"></i>
                                <div style="margin-top: 10px;">实时视频监控</div>
                                <div style="font-size: 12px; margin-top: 5px;">能耗超标 +25%</div>
                            </div>
                            <div class="status-badge status-danger">异常预警</div>
                        </div>
                        <div class="card">
                            <h5><i class="fas fa-wind"></i> 通风机房 - 轻微异常</h5>
                            <div class="video-placeholder">
                                <i class="fas fa-play-circle fa-3x"></i>
                                <div style="margin-top: 10px;">实时视频监控</div>
                                <div style="font-size: 12px; margin-top: 5px;">能耗波动 +8%</div>
                            </div>
                            <div class="status-badge status-warning">轻微异常</div>
                        </div>
                    </div>

                    <div class="card">
                        <h5><i class="fas fa-list"></i> 预警记录</h5>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>位置</th>
                                    <th>预警类型</th>
                                    <th>严重程度</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>14:32</td>
                                    <td>电梯机房</td>
                                    <td>能耗超标</td>
                                    <td><span class="status-badge status-danger">严重</span></td>
                                    <td><button class="btn btn-primary"
                                            style="padding: 3px 8px; font-size: 11px;">回放</button></td>
                                </tr>
                                <tr>
                                    <td>13:45</td>
                                    <td>通风机房</td>
                                    <td>设备异常</td>
                                    <td><span class="status-badge status-warning">中等</span></td>
                                    <td><button class="btn btn-primary"
                                            style="padding: 3px 8px; font-size: 11px;">回放</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 8. 系统设置页 -->
            <div class="screen">
                <div class="screen-header">
                    <div class="screen-title">
                        <i class="fas fa-cog"></i>
                        系统设置
                    </div>
                    <button class="btn btn-success" style="font-size: 12px;">
                        <i class="fas fa-save"></i> 保存设置
                    </button>
                </div>
                <div class="screen-content">
                    <div class="nav-bar">
                        <div class="nav-item active">基础设置</div>
                        <div class="nav-item">用户管理</div>
                        <div class="nav-item">系统日志</div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-sliders-h"></i> 能耗监控设置</h4>
                        <div class="grid-2">
                            <div class="form-group">
                                <label class="form-label">数据采集频率</label>
                                <select class="form-control">
                                    <option>每分钟</option>
                                    <option selected>每5分钟</option>
                                    <option>每15分钟</option>
                                    <option>每30分钟</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">数据保存周期</label>
                                <select class="form-control">
                                    <option>30天</option>
                                    <option>90天</option>
                                    <option selected>1年</option>
                                    <option>永久保存</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-bell"></i> 通知设置</h4>
                        <div class="form-group">
                            <label class="form-label">预警通知方式</label>
                            <div>
                                <label><input type="checkbox" checked> 系统内通知</label><br>
                                <label><input type="checkbox" checked> 邮件通知</label><br>
                                <label><input type="checkbox"> 短信通知</label><br>
                                <label><input type="checkbox" checked> 微信通知</label>
                            </div>
                        </div>
                        <div class="grid-2">
                            <div class="form-group">
                                <label class="form-label">通知邮箱</label>
                                <input type="email" class="form-control" value="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">通知手机</label>
                                <input type="tel" class="form-control" value="138****8888">
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h4><i class="fas fa-users"></i> 系统信息</h4>
                        <table class="data-table">
                            <tbody>
                                <tr>
                                    <td><strong>系统版本</strong></td>
                                    <td>v2.1.3</td>
                                </tr>
                                <tr>
                                    <td><strong>数据库版本</strong></td>
                                    <td>MySQL 8.0.28</td>
                                </tr>
                                <tr>
                                    <td><strong>最后更新</strong></td>
                                    <td>2024-06-15 10:30:00</td>
                                </tr>
                                <tr>
                                    <td><strong>在线用户</strong></td>
                                    <td>12人</td>
                                </tr>
                                <tr>
                                    <td><strong>系统状态</strong></td>
                                    <td><span class="status-badge status-normal">运行正常</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
</body>

</html>